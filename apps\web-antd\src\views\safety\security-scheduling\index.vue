<script lang="ts" setup>
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Button, TabPane, Tabs } from 'ant-design-vue';

import GroupByCalendar from './group-by-calendar.vue';
import GroupByPersonnel from './group-by-personnel.vue';

const activeKey = ref('1');
</script>

<template>
  <Page auto-content-height>
    <Tabs
      v-model:active-key="activeKey"
      class="bg-white"
      :tab-bar-style="{ padding: '0 16px' }"
    >
      <TabPane key="1" tab="日历视图" class="p-3">
        <GroupByCalendar />
      </TabPane>
      <TabPane key="2" tab="人员视图" class="p-3">
        <GroupByPersonnel />
      </TabPane>
      <template #rightExtra>
        <Button type="primary">排班配置</Button>
      </template>
    </Tabs>
  </Page>
</template>
