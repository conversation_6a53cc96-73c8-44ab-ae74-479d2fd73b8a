<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { Button, DatePicker, RadioButton, RadioGroup } from 'ant-design-vue';
import dayjs from 'dayjs';

import $BatchSchedulingModal from './batch-scheduling-modal.vue';

const mode = defineModel<'month' | 'week'>('mode', {
  default: 'week',
});
const date = defineModel('date', {
  default: dayjs(),
});

const Label = {
  week: '周',
  month: '月',
};

const [BatchSchedulingModal, modalApi] = useVbenModal({
  connectedComponent: $BatchSchedulingModal,
});
</script>

<template>
  <div class="flex justify-between pb-3">
    <div>
      <Button type="primary">批量排班</Button>
    </div>
    <div class="flex gap-2">
      <slot></slot>
      <Button @click="date = date.subtract(1, mode)">
        上一{{ Label[mode] }}
      </Button>
      <Button @click="date = date.add(1, mode)"> 下一{{ Label[mode] }} </Button>
      <DatePicker v-model:value="date" :picker="mode" :allow-clear="false" />
      <RadioGroup v-model:value="mode">
        <RadioButton value="week">周</RadioButton>
        <RadioButton value="month">月</RadioButton>
      </RadioGroup>
    </div>
    <BatchSchedulingModal />
  </div>
</template>
