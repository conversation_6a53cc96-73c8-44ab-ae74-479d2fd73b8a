<script lang="ts" setup>
import { computed, ref } from 'vue';

import { Calendar, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import CommonHeader from './common-header.vue';

const mode = ref<'month' | 'week'>('week');
const date = ref(dayjs());
const calendarRange = computed(() => {
  let start = date.value;
  let end = start;
  if (mode.value === 'month') {
    start = date.value.startOf('month');
    end = date.value.endOf('month');
  }
  return [start.startOf('week'), end.endOf('week')];
});
const data = ref([
  {
    date: '2025-07-01',
    content: [
      { name: '张三、李四', time: '07:00~14:00' },
      { name: '张三、李四', time: '14:00~23:00' },
      { name: '张三、李四', time: '23:00~07:00' },
    ],
  },
]);
const data2 = computed(() => {
  return Object.fromEntries(
    data.value.map((item) => [item.date, item.content]),
  );
});
</script>

<template>
  <Calendar v-model:value="date" mode="month">
    <template #headerRender>
      <CommonHeader v-model:mode="mode" v-model:date="date" />
    </template>
    <template #dateFullCellRender="{ current }">
      <div
        v-if="
          current.isAfter(calendarRange[0]) &&
          current.isBefore(calendarRange[1])
        "
        class="ant-picker-cell-inner ant-picker-calendar-date"
      >
        <div class="flex justify-between">
          <Tag v-if="data2[current.format('YYYY-MM-DD')]" color="success">
            已排班
          </Tag>
          <Tag v-else color="warning">未排班</Tag>
          <div class="ant-picker-calendar-date-value">{{ current.date() }}</div>
        </div>
        <div class="ant-picker-calendar-date-content">
          <template
            v-for="(content, index) in data2[current.format('YYYY-MM-DD')]"
            :key="index"
          >
            <div>{{ content.time }} | {{ content.name }}</div>
          </template>
        </div>
      </div>
    </template>
  </Calendar>
</template>
