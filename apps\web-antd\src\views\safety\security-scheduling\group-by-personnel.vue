<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { PageQuery } from '#/api/common';
import type { OperationLog } from '#/api/monitor/operlog/model';

import { computed, ref, watch } from 'vue';

import dayjs, { Dayjs } from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

import { addSortParams, useVbenVxeGrid } from '#/adapter/vxe-table';
import { operLogList } from '#/api/monitor/operlog';

import CommonHeader from './common-header.vue';
import { columns } from './data';

// 扩展 dayjs 插件
dayjs.extend(isSameOrBefore);

const mode = ref<'month' | 'week'>('week');
const date = ref(dayjs());

const gridOptions: VxeGridProps<OperationLog> = {
  columns,
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page, sorts }, formValues = {}) => {
        const params: PageQuery = {
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        };
        // 添加排序参数
        addSortParams(params, sorts);
        return await operLogList(params);
      },
    },
  },
  rowConfig: {
    keyField: 'operId',
  },
  toolbarConfig: {
    enabled: false,
  },
  id: 'security-scheduling',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  gridOptions,
  gridClass: 'p-0',
});
const dateRange = computed(() => {
  return [date.value.startOf(mode.value), date.value.endOf(mode.value)] as [
    Dayjs,
    Dayjs,
  ];
});

// 根据dateRange生成期间所有日期字符串数组
const dateList = computed(() => {
  const [startDate, endDate] = dateRange.value;
  const dates: string[] = [];
  let current = startDate.clone();

  while (current.isSameOrBefore(endDate, 'day')) {
    dates.push(current.format('YYYY-MM-DD'));
    current = current.add(1, 'day');
  }

  return dates;
});
watch(dateList, () => {
  tableApi.setGridOptions({
    columns: dateList.value.map((item) => ({
      field: item,
      title: item,
    })),
  });
});
</script>

<template>
  <div>
    <CommonHeader v-model:mode="mode" v-model:date="date" />
    <BasicTable />
  </div>
</template>
