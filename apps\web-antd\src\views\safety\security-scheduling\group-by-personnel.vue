<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { PageQuery } from '#/api/common';
import type { OperationLog } from '#/api/monitor/operlog/model';

import { computed, ref, watch } from 'vue';

import dayjs from 'dayjs';

import { addSortParams, useVbenVxeGrid } from '#/adapter/vxe-table';
import { operLogList } from '#/api/monitor/operlog';

import CommonHeader from './common-header.vue';
import { columns } from './data';

const mode = ref<'month' | 'week'>('week');
const date = ref(dayjs());

const gridOptions: VxeGridProps<OperationLog> = {
  columns,
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page, sorts }, formValues = {}) => {
        const params: PageQuery = {
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        };
        // 添加排序参数
        addSortParams(params, sorts);
        return await operLogList(params);
      },
    },
  },
  rowConfig: {
    keyField: 'operId',
  },
  toolbarConfig: {
    enabled: false,
  },
  id: 'security-scheduling',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  gridOptions,
  gridClass: 'p-0',
});
const dateRange = computed(() => {
  return [date.value.startOf(mode.value), date.value.endOf(mode.value)];
});
watch(dateRange, () => {
  tableApi.query();
});
</script>

<template>
  <div>
    <CommonHeader v-model:mode="mode" v-model:date="date" />
    <BasicTable />
  </div>
</template>
