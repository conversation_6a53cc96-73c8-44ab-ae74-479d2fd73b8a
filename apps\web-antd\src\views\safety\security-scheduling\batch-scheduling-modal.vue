<script setup lang="ts">
import type { LoginLog } from '#/api/monitor/logininfo/model';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

const loginInfo = ref<LoginLog>();
const [BasicModal, modalApi] = useVbenModal({
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      return null;
    }
    const record = modalApi.getData() as LoginLog;
    loginInfo.value = record;
  },
  onClosed() {
    loginInfo.value = undefined;
  },
});
</script>

<template>
  <BasicModal
    :footer="false"
    :fullscreen-button="false"
    class="w-[550px]"
    title="登录日志"
  >
    111
  </BasicModal>
</template>
